import { create } from "zustand";

interface DialogStore {
  openDialogs: Map<number, string>;
  addDialog: (id: string) => void;
  removeDialog: (id: string) => void;
  getIsLastDialog: (id: string) => boolean;
  getIsFirstDialog: (id: string) => boolean;
}

export const useDialogStore = create<DialogStore>((set, get) => ({
  openDialogs: new Map(),

  addDialog: (id) => {
    set((state) => {
      const newMap = new Map(state.openDialogs);
      newMap.set(newMap.size, id);
      return { openDialogs: newMap };
    });
  },

  removeDialog: (id) => {
    set((state) => {
      const newMap = new Map(state.openDialogs);
      // Find and remove the dialog by id
      for (const [key, value] of newMap) {
        if (value === id) {
          newMap.delete(key);
          break;
        }
      }

      // Reindex the remaining dialogs to maintain sequential order
      const reindexedMap = new Map();
      let index = 0;
      for (const [, dialogId] of newMap) {
        reindexedMap.set(index, dialogId);
        index++;
      }

      return { openDialogs: reindexedMap };
    });
  },

  getIsLastDialog: (id) => {
    const { openDialogs } = get();
    return openDialogs.get(openDialogs.size - 1) === id;
  },

  getIsFirstDialog: (id) => {
    const { openDialogs } = get();
    return openDialogs.get(0) === id;
  },
}));
