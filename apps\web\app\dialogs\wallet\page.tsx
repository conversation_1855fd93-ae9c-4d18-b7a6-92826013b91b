"use client";

import { useState } from "react";
import { WalletDialog } from "@/components/wallet.dialog";
import { WalletButton } from "@workspace/ui/components/shell/wallet-button";

export default function WalletDialogPage() {
  const [walletOpen, setWalletOpen] = useState(false);

  return (
    <div className="p-8">
      <WalletButton onClick={() => setWalletOpen(true)} />
      <WalletDialog
        open={walletOpen}
        onOpenChange={setWalletOpen}
        historyKey=""
      />
    </div>
  );
}
