# Dialog Store

A global state management solution for handling nested dialogs using Zustand.

## Features

- **Global State**: No need to pass context providers everywhere
- **Automatic Rerendering**: Components automatically rerender when dialog state changes
- **Overlay Management**: Only the first dialog shows the overlay, subsequent dialogs hide it
- **Last Dialog Visibility**: Only the topmost dialog is visible, others are hidden with opacity-0
- **Proper Cleanup**: Dialogs are automatically removed from state when closed

## Usage

### Basic Usage

```tsx
import { useDialogStore } from "@workspace/ui/stores/dialog-store";

function MyComponent() {
  const { openDialogs, addDialog, removeDialog } = useDialogStore();
  
  // Access current dialog state
  console.log(`${openDialogs.size} dialogs are open`);
}
```

### Dialog Component Integration

The dialog components automatically use the store, so you don't need to change how you use them:

```tsx
import { Dialog, DialogContent, DialogTrigger } from "@workspace/ui/components/dialog";

function MyDialog() {
  const [open, setOpen] = useState(false);
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>Open Dialog</DialogTrigger>
      <DialogContent>
        {/* Your dialog content */}
      </DialogContent>
    </Dialog>
  );
}
```

### Accessing Dialog State Globally

You can access dialog state from any component without prop drilling:

```tsx
function DialogMonitor() {
  const { openDialogs, getIsLastDialog, getIsFirstDialog } = useDialogStore();
  
  return (
    <div>
      <p>Open dialogs: {openDialogs.size}</p>
      {Array.from(openDialogs.entries()).map(([level, id]) => (
        <div key={level}>
          Dialog {level}: {id}
          {getIsFirstDialog(id) && " (Shows overlay)"}
          {getIsLastDialog(id) && " (Visible)"}
        </div>
      ))}
    </div>
  );
}
```

## Store API

### State

- `openDialogs: Map<number, string>` - Map of dialog levels to dialog IDs

### Actions

- `addDialog(id: string)` - Add a dialog to the stack
- `removeDialog(id: string)` - Remove a dialog from the stack and reindex
- `getIsLastDialog(id: string)` - Check if dialog is the topmost (visible) dialog
- `getIsFirstDialog(id: string)` - Check if dialog is the first (shows overlay) dialog

## How It Works

1. **Dialog Registration**: When a dialog opens, it calls `addDialog(id)` with its unique ID
2. **Level Assignment**: Dialogs are assigned sequential levels (0, 1, 2, ...)
3. **Visibility Logic**: 
   - Only the dialog at the highest level (`openDialogs.size - 1`) is visible
   - Other dialogs have `opacity-0` applied
4. **Overlay Logic**: 
   - Only the first dialog (level 0) shows the overlay
   - Other dialogs hide their overlay with `opacity-0`
5. **Cleanup**: When a dialog closes, it's removed and remaining dialogs are reindexed

## Migration from Context

The new system is a drop-in replacement. Your existing dialog usage doesn't need to change:

```tsx
// Before (still works)
<Dialog open={open} onOpenChange={setOpen}>
  <DialogContent>Content</DialogContent>
</Dialog>

// After (same usage, but now uses global state)
<Dialog open={open} onOpenChange={setOpen}>
  <DialogContent>Content</DialogContent>
</Dialog>
```

## Benefits

1. **No Context Providers**: No need to wrap your app in dialog context providers
2. **Global Access**: Access dialog state from anywhere in your component tree
3. **Automatic Updates**: Components rerender automatically when dialog state changes
4. **Better Performance**: Zustand is optimized for minimal rerenders
5. **Developer Experience**: Easier debugging with global state visibility
