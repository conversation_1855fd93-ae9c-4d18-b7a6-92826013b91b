"use client";

import { useState } from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { useDialogHistory } from "@workspace/ui/hooks/use-dialog-history";

export default function Page() {
  const [open1, setOpen1] = useState(false);
  const [open2, setOpen2] = useState(false);
  const [open3, setOpen3] = useState(false);
  const [open4, setOpen4] = useState(false);

  // History-enabled dialog states
  const [level1Open, setLevel1Open] = useDialogHistory("level1");
  const [level2Open, setLevel2Open] = useDialogHistory("level2");
  const [level3Open, setLevel3Open] = useDialogHistory("level3");
  const [level4Open, setLevel4Open] = useDialogHistory("level4");

  return (
    <>
      <div className="p-6 space-y-4">
        <div>
          <h2 className="text-lg font-semibold mb-2">
            Normal Dialogs (No History)
          </h2>
          <Button variant="primary" onClick={() => setOpen1(true)}>
            {"1. Seviyeyi Aç (Normal)"}
          </Button>
        </div>
        <div>
          <h2 className="text-lg font-semibold mb-2">
            History-Enabled Dialogs
          </h2>
          <p className="text-sm text-muted-foreground mb-2">
            These dialogs use URL state management. Try opening them and using
            browser back/forward buttons!
          </p>
          <Button variant="primary" onClick={() => setLevel1Open(true)}>
            {"1. Seviyeyi Aç (History)"}
          </Button>
        </div>
      </div>

      {/* Normal dialogs without history */}
      <Dialog open={open1} onOpenChange={setOpen1}>
        <DialogContent className="sm:max-w-[1000px] h-[700px]">
          <DialogHeader>
            <DialogTitle>{"1. Seviye Diyalog"}</DialogTitle>
            <DialogDescription>
              {
                "Bu sayfa iç içe geçmiş diyalogları (nested dialogs) gösterir. Toplam 4 seviye vardır."
              }
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="primary" onClick={() => setOpen2(true)}>
              {"2. Seviyeyi Aç"}
            </Button>
          </DialogFooter>
        </DialogContent>

        {/* Level 2 */}
        <Dialog open={open2} onOpenChange={setOpen2}>
          <DialogContent className="sm:max-w-[900px] h-[600px]">
            <DialogHeader>
              <DialogTitle>{"2. Seviye Diyalog"}</DialogTitle>
              <DialogDescription>
                {
                  "Üstteki diyaloğun içinde açıldı. Bir seviye daha açabilirsiniz."
                }
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="secondary">{"2. Seviyeyi Kapat"}</Button>
              </DialogClose>
              <Button variant="primary" onClick={() => setOpen3(true)}>
                {"3. Seviyeyi Aç"}
              </Button>
            </DialogFooter>
          </DialogContent>
          {/* Level 3 */}
          <Dialog open={open3} onOpenChange={setOpen3}>
            <DialogContent className="sm:max-w-[800px] h-[500px]">
              <DialogHeader>
                <DialogTitle>{"3. Seviye Diyalog"}</DialogTitle>
                <DialogDescription>
                  {
                    "Bir seviye daha içe gidebilirsiniz. Sonraki seviye son seviyedir."
                  }
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="secondary">{"3. Seviyeyi Kapat"}</Button>
                </DialogClose>
                <Button variant="primary" onClick={() => setOpen4(true)}>
                  {"4. Seviyeyi Aç"}
                </Button>
              </DialogFooter>
            </DialogContent>
            {/* Level 4 */}
            <Dialog open={open4} onOpenChange={setOpen4}>
              <DialogContent className="sm:max-w-[700px] h-[400px]">
                <DialogHeader>
                  <DialogTitle>{"4. Seviye Diyalog"}</DialogTitle>
                  <DialogDescription>
                    {
                      "Bu son seviye. Kapatınca bir önceki seviyeye geri dönersiniz."
                    }
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter className="gap-2">
                  <DialogClose asChild>
                    <Button variant="secondary">{"4. Seviyeyi Kapat"}</Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </Dialog>
        </Dialog>
      </Dialog>

      {/* History-enabled dialogs */}
      <Dialog
        open={level1Open}
        onOpenChange={setLevel1Open}
        historyKey="level1"
      >
        <DialogContent className="sm:max-w-[1000px] h-[700px]">
          <DialogHeader>
            <DialogTitle>{"1. Seviye Diyalog (History)"}</DialogTitle>
            <DialogDescription>
              {
                "Bu diyalog URL state kullanır. Tarayıcı geri/ileri butonlarını deneyin!"
              }
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="primary" onClick={() => setLevel2Open(true)}>
              {"2. Seviyeyi Aç"}
            </Button>
          </DialogFooter>
        </DialogContent>

        {/* Level 2 with history */}
        <Dialog
          open={level2Open}
          onOpenChange={setLevel2Open}
          historyKey="level2"
        >
          <DialogContent className="sm:max-w-[900px] h-[600px]">
            <DialogHeader>
              <DialogTitle>{"2. Seviye Diyalog (History)"}</DialogTitle>
              <DialogDescription>
                {
                  "URL'de değişiklikleri görebilirsiniz. Sayfayı yenileyin, durum korunur!"
                }
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="secondary">{"2. Seviyeyi Kapat"}</Button>
              </DialogClose>
              <Button variant="primary" onClick={() => setLevel3Open(true)}>
                {"3. Seviyeyi Aç"}
              </Button>
            </DialogFooter>
          </DialogContent>

          {/* Level 3 with history */}
          <Dialog
            open={level3Open}
            onOpenChange={setLevel3Open}
            historyKey="level3"
          >
            <DialogContent className="sm:max-w-[800px] h-[500px]">
              <DialogHeader>
                <DialogTitle>{"3. Seviye Diyalog (History)"}</DialogTitle>
                <DialogDescription>
                  {"Bu seviyede de URL state korunur. Geri butonunu deneyin!"}
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="secondary">{"3. Seviyeyi Kapat"}</Button>
                </DialogClose>
                <Button variant="primary" onClick={() => setLevel4Open(true)}>
                  {"4. Seviyeyi Aç"}
                </Button>
              </DialogFooter>
            </DialogContent>

            {/* Level 4 with history */}
            <Dialog
              open={level4Open}
              onOpenChange={setLevel4Open}
              historyKey="level4"
            >
              <DialogContent className="sm:max-w-[700px] h-[400px]">
                <DialogHeader>
                  <DialogTitle>{"4. Seviye Diyalog (History)"}</DialogTitle>
                  <DialogDescription>
                    {"Son seviye! URL'deki tüm parametreleri görebilirsiniz."}
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter className="gap-2">
                  <DialogClose asChild>
                    <Button variant="secondary">{"4. Seviyeyi Kapat"}</Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </Dialog>
        </Dialog>
      </Dialog>
    </>
  );
}
