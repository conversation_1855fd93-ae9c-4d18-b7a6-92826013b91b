import { useQueryState } from 'nuqs'

export function useDialogHistory(dialogKey: string) {
  const [isOpen, setIsOpen] = useQueryState(dialogKey, {
    defaultValue: false,
    parse: (value) => value === 'true',
    serialize: (value) => value ? 'true' : null, // null removes from URL
    history: 'push', // Creates history entries
    clearOnDefault: true // Removes param when false
  })

  return [isOpen, setIsOpen] as const
}
