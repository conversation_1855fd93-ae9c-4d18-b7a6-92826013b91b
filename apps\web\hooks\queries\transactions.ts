import { useQuery } from "@tanstack/react-query";

export interface Transaction {
  id: string;
  date: string;
  type: string;
  description: string;
  amount: number;
  newBalance: number;
  status?: string;
}

export interface TransactionData {
  [month: string]: Transaction[];
}

// Generate realistic transaction ID
const generateTransactionId = (
  index: number,
  year: number,
  month: number,
): string => {
  const yearStr = year.toString();
  const monthStr = month.toString().padStart(2, "0");
  const dayStr = Math.floor(Math.random() * 28 + 1)
    .toString()
    .padStart(2, "0");
  const sequenceStr = (index + 1).toString().padStart(6, "0");
  return `${yearStr}${monthStr}${dayStr}${sequenceStr}`;
};

// Mock function to simulate API call for transaction data
const fetchTransactions = async (): Promise<{
  sodaTransactions: TransactionData;
  capsTransactions: TransactionData;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // Generate realistic transaction data
  const sodaTransactions: TransactionData = {
    "2025-01": [
      {
        id: generateTransactionId(0, 2025, 1),
        date: "2025-01-31",
        type: "Yatırım",
        description: "Banka Havalesi ile Para Yatırma",
        amount: 2000,
        newBalance: 5150,
      },
      {
        id: generateTransactionId(1, 2025, 1),
        date: "2025-01-30",
        type: "Ödeme",
        description: "Aktivite Ödemesi - PUBG Mobile Turnuvası",
        amount: -150,
        newBalance: 3150,
      },
      {
        id: generateTransactionId(2, 2025, 1),
        date: "2025-01-29",
        type: "Kazanç",
        description: "Günlük Görev Tamamlama Ödülü",
        amount: 75,
        newBalance: 3300,
      },
      {
        id: generateTransactionId(3, 2025, 1),
        date: "2025-01-28",
        type: "Çekim",
        description: "Para Çekme İşlemi Tamamlandı",
        amount: -500,
        newBalance: 3225,
      },
      {
        id: generateTransactionId(4, 2025, 1),
        date: "2025-01-27",
        type: "Kazanç",
        description: "Haftalık Turnuva Ödülü - Valorant",
        amount: 300,
        newBalance: 3725,
      },
      {
        id: generateTransactionId(5, 2025, 1),
        date: "2025-01-26",
        type: "Ödeme",
        description: "Aktivite Katılım Ücreti - CS2 Maçı",
        amount: -50,
        newBalance: 3425,
      },
      {
        id: generateTransactionId(6, 2025, 1),
        date: "2025-01-25",
        type: "Yatırım",
        description: "Kredi Kartı ile Para Yatırma",
        amount: 1000,
        newBalance: 3475,
      },
      {
        id: generateTransactionId(7, 2025, 1),
        date: "2025-01-24",
        type: "Kazanç",
        description: "Arkadaş Davet Bonusu",
        amount: 100,
        newBalance: 2475,
      },
      {
        id: generateTransactionId(8, 2025, 1),
        date: "2025-01-23",
        type: "Ödeme",
        description: "Aktivite Ödemesi - League of Legends Maçı",
        amount: -75,
        newBalance: 2375,
      },
      {
        id: generateTransactionId(9, 2025, 1),
        date: "2025-01-22",
        type: "Kazanç",
        description: "Günlük Görev Tamamlama Ödülü",
        amount: 50,
        newBalance: 2450,
      },
      {
        id: generateTransactionId(10, 2025, 1),
        date: "2025-01-21",
        type: "Yatırım",
        description: "Banka Havalesi ile Para Yatırma",
        amount: 500,
        newBalance: 2400,
      },
      {
        id: generateTransactionId(11, 2025, 1),
        date: "2025-01-20",
        type: "Kazanç",
        description: "Haftalık Turnuva Ödülü - Dota 2",
        amount: 200,
        newBalance: 1900,
      },
      {
        id: generateTransactionId(12, 2025, 1),
        date: "2025-01-19",
        type: "Ödeme",
        description: "Aktivite Katılım Ücreti - Fortnite Turnuvası",
        amount: -100,
        newBalance: 1700,
      },
      {
        id: generateTransactionId(13, 2025, 1),
        date: "2025-01-18",
        type: "Kazanç",
        description: "Günlük Görev Tamamlama Ödülü",
        amount: 25,
        newBalance: 1800,
      },
      {
        id: generateTransactionId(14, 2025, 1),
        date: "2025-01-17",
        type: "Hediye",
        description: "Arkadaştan Hediye Soda",
        amount: 150,
        newBalance: 1775,
      },
      {
        id: generateTransactionId(15, 2025, 1),
        date: "2025-01-16",
        type: "Ödeme",
        description: "Aktivite Ödemesi - Apex Legends Maçı",
        amount: -80,
        newBalance: 1625,
      },
      {
        id: generateTransactionId(16, 2025, 1),
        date: "2025-01-15",
        type: "Kazanç",
        description: "Haftalık Turnuva Ödülü - CS2",
        amount: 250,
        newBalance: 1705,
      },
      {
        id: generateTransactionId(17, 2025, 1),
        date: "2025-01-14",
        type: "Yatırım",
        description: "Kredi Kartı ile Para Yatırma",
        amount: 750,
        newBalance: 1455,
      },
      {
        id: generateTransactionId(18, 2025, 1),
        date: "2025-01-13",
        type: "Kazanç",
        description: "Günlük Görev Tamamlama Ödülü",
        amount: 30,
        newBalance: 705,
      },
      {
        id: generateTransactionId(19, 2025, 1),
        date: "2025-01-12",
        type: "Ödeme",
        description: "Aktivite Katılım Ücreti - Rocket League",
        amount: -60,
        newBalance: 675,
      },
      {
        id: generateTransactionId(20, 2025, 1),
        date: "2025-01-11",
        type: "Kazanç",
        description: "Arkadaş Davet Bonusu",
        amount: 100,
        newBalance: 735,
      },
      {
        id: generateTransactionId(21, 2025, 1),
        date: "2025-01-10",
        type: "Gönderim",
        description: "Arkadaşa Soda Gönderimi",
        amount: -50,
        newBalance: 635,
      },
      {
        id: generateTransactionId(22, 2025, 1),
        date: "2025-01-09",
        type: "Kazanç",
        description: "Günlük Görev Tamamlama Ödülü",
        amount: 40,
        newBalance: 685,
      },
      {
        id: generateTransactionId(23, 2025, 1),
        date: "2025-01-08",
        type: "Yatırım",
        description: "Banka Havalesi ile Para Yatırma",
        amount: 300,
        newBalance: 645,
      },
      {
        id: generateTransactionId(24, 2025, 1),
        date: "2025-01-07",
        type: "Ödeme",
        description: "Aktivite Ödemesi - FIFA Turnuvası",
        amount: -120,
        newBalance: 345,
      },
      {
        id: generateTransactionId(25, 2025, 1),
        date: "2025-01-06",
        type: "Kazanç",
        description: "Haftalık Turnuva Ödülü - Overwatch",
        amount: 180,
        newBalance: 465,
      },
      {
        id: generateTransactionId(26, 2025, 1),
        date: "2025-01-05",
        type: "Hediye",
        description: "Yeni Yıl Bonusu",
        amount: 200,
        newBalance: 285,
      },
      {
        id: generateTransactionId(27, 2025, 1),
        date: "2025-01-04",
        type: "Ödeme",
        description: "Aktivite Katılım Ücreti - Tekken 8",
        amount: -90,
        newBalance: 85,
      },
      {
        id: generateTransactionId(28, 2025, 1),
        date: "2025-01-03",
        type: "Kazanç",
        description: "Günlük Görev Tamamlama Ödülü",
        amount: 35,
        newBalance: 175,
      },
      {
        id: generateTransactionId(29, 2025, 1),
        date: "2025-01-02",
        type: "Yatırım",
        description: "İlk Yatırım Bonusu",
        amount: 500,
        newBalance: 140,
      },
      {
        id: generateTransactionId(30, 2025, 1),
        date: "2025-01-01",
        type: "Hediye",
        description: "Hoş Geldin Bonusu",
        amount: 100,
        newBalance: 100,
      },
    ],
    "2024-12": [
      {
        id: generateTransactionId(0, 2024, 12),
        date: "2024-12-30",
        type: "Yatırım",
        description: "Banka Havalesi ile Para Yatırma",
        amount: 1500,
        newBalance: 2375,
      },
      {
        id: generateTransactionId(1, 2024, 12),
        date: "2024-12-28",
        type: "Kazanç",
        description: "Aylık Turnuva Ödülü - League of Legends",
        amount: 500,
        newBalance: 875,
      },
      {
        id: generateTransactionId(2, 2024, 12),
        date: "2024-12-25",
        type: "Ödeme",
        description: "Özel Etkinlik Katılım Ücreti",
        amount: -200,
        newBalance: 375,
      },
    ],
  };

  const capsTransactions: TransactionData = {
    "2025-01": [
      {
        id: generateTransactionId(0, 2025, 1),
        date: "2025-01-31",
        type: "Kazanç",
        description: "Günlük Giriş Bonusu",
        amount: 5,
        newBalance: 19,
      },
      {
        id: generateTransactionId(1, 2025, 1),
        date: "2025-01-30",
        type: "Ödeme",
        description: "Özel Skin Satın Alma",
        amount: -8,
        newBalance: 14,
      },
      {
        id: generateTransactionId(2, 2025, 1),
        date: "2025-01-29",
        type: "Kazanç",
        description: "Haftalık Görev Tamamlama",
        amount: 12,
        newBalance: 22,
      },
      {
        id: generateTransactionId(3, 2025, 1),
        date: "2025-01-28",
        type: "Kazanç",
        description: "Turnuva Katılım Ödülü",
        amount: 3,
        newBalance: 10,
      },
      {
        id: generateTransactionId(4, 2025, 1),
        date: "2025-01-27",
        type: "Ödeme",
        description: "Premium Özellik Aktivasyonu",
        amount: -15,
        newBalance: 7,
      },
    ],
    "2024-12": [
      {
        id: generateTransactionId(0, 2024, 12),
        date: "2024-12-30",
        type: "Kazanç",
        description: "Yılsonu Bonusu",
        amount: 20,
        newBalance: 22,
      },
      {
        id: generateTransactionId(1, 2024, 12),
        date: "2024-12-25",
        type: "Kazanç",
        description: "Noel Özel Etkinlik Ödülü",
        amount: 10,
        newBalance: 2,
      },
    ],
    "2025-03": [
      {
        id: generateTransactionId(0, 2025, 3),
        date: "2025-03-12",
        type: "Kazanç",
        description: "Haftalık Görev Tamamlama Bonusu",
        amount: 10,
        newBalance: 32,
      },
    ],
  };

  return {
    sodaTransactions,
    capsTransactions,
  };
};

// Balance interface
export interface BalanceData {
  sodaBalance: number;
  capsBalance: number;
}

// Mock function to simulate API call for balance data
const fetchBalances = async (): Promise<BalanceData> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // Return mock balance data
  return {
    sodaBalance: 5150,
    capsBalance: 2850,
  };
};

export const useTransactions = () => {
  return useQuery({
    queryKey: ["transactions"],
    queryFn: fetchTransactions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useBalances = () => {
  return useQuery({
    queryKey: ["balances"],
    queryFn: fetchBalances,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
