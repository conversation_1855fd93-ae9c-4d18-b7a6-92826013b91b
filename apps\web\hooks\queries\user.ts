import { useQuery } from "@tanstack/react-query";

export interface User {
  nickname: string;
  username: string;
  is_kyc_approved: boolean;
  is_iban_saved: boolean;
}

// Mock function to simulate API call for user data
const fetchUser = async (): Promise<User> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Return mock user data
  return {
    nickname: "HikariBlaze",
    username: "hikari_",
    is_kyc_approved: false,
    is_iban_saved: false,
  };
};

export const useUser = () => {
  return useQuery({
    queryKey: ["user"],
    queryFn: fetchUser,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
