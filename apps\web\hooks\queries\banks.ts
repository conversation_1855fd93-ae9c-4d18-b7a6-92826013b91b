import { useQuery } from "@tanstack/react-query";

export interface Bank {
  name: string;
  image: string;
  accountName: string;
  iban: string;
  description?: string;
}

// Mock function to simulate API call for bank data
const fetchBanks = async (): Promise<{
  banks: Bank[];
  descriptionCode: string;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 3000));

  return {
    descriptionCode: "ES134KVK",
    banks: [
      {
        name: "Yapı Kredi",
        image: "/banks/yapikredi.jpg",
        accountName: "ESENPAI YAZILIM SAN. VE TIC. LTD.",
        iban: "TR32 0010 0094 1000 6117 8688 16",
        description: "Yapı ve Kredi Bankası A.Ş.",
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        image: "/banks/garanti.jpg",
        accountName: "ESENPAI YAZILIM SAN. VE TIC. LTD.",
        iban: "TR45 0062 0100 0000 0123 4567 89",
        description: "Türkiye Garanti Bankası A.Ş.",
      },
      {
        name: "İş Bankası",
        image: "/banks/isbankasi.jpg",
        accountName: "ESENPAI YAZILIM SAN. VE TIC. LTD.",
        iban: "TR64 0001 0001 0000 0098 7654 32",
        description: "Türkiye İş Bankası A.Ş.",
      },
      {
        name: "Ziraat Bankası",
        image: "/banks/ziraatbankasi.jpg",
        accountName: "ESENPAI YAZILIM SAN. VE TIC. LTD.",
        iban: "TR98 0001 0001 0000 0012 3456 78",
        description: "Türkiye Cumhuriyeti Ziraat Bankası A.Ş.",
      },
      {
        name: "Halkbank",
        image: "/banks/halkbank.jpg",
        accountName: "ESENPAI YAZILIM SAN. VE TIC. LTD.",
        iban: "TR12 0001 2009 4500 0058 0000 17",
        description: "Türkiye Halk Bankası A.Ş.",
      },
      {
        name: "VakıfBank",
        image: "/banks/vakifbank.jpg",
        accountName: "ESENPAI YAZILIM SAN. VE TIC. LTD.",
        iban: "TR88 0001 5001 5800 7300 1234 56",
        description: "Türkiye Vakıflar Bankası T.A.O.",
      },
    ],
  };
};

export const useBanks = () => {
  return useQuery({
    queryKey: ["banks"],
    queryFn: fetchBanks,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
};
