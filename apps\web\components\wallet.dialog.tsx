"use client";

import * as React from "react";
import { useState } from "react";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@workspace/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  ReceiptText,
  X,
  ChevronDown,
  Filter,
  Search as SearchIcon,
  Download,
} from "lucide-react";
import { KYCDialog } from "./kyc.dialog";
import { IbanDialog } from "./iban.dialog";
import { BankTransferDialog } from "./bank-transfer.dialog";
import { WithdrawalDialog } from "./withdrawal.dialog";
import { useTransactions, useBalances } from "@/hooks/queries/transactions";
import { useUser } from "@/hooks/queries/user";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Search } from "@workspace/ui/components/search";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";

// Transaction type definitions
export interface Transaction {
  id: string;
  date: string;
  type: string;
  description: string;
  amount: number;
  newBalance: number;
  status?: string;
}

export interface TransactionData {
  [month: string]: Transaction[];
}

export interface WalletDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onDepositClick?: () => void;
  onBankTransferClick?: () => void;
  onWithdrawClick?: () => void;
  onEarnClick?: () => void;
  historyKey?: string;
  children?: React.ReactNode;
}

export function WalletDialog({
  open = false,
  onOpenChange,
  onBankTransferClick,
  onWithdrawClick,
  onEarnClick,
  historyKey,
  children,
}: WalletDialogProps) {
  const effectiveHistoryKey =
    historyKey === "" ? undefined : (historyKey ?? "wallet");
  const { data: transactionData, isLoading: transactionsLoading } =
    useTransactions();
  const { data: balanceData, isLoading: balancesLoading } = useBalances();
  const { data: userData, isLoading: userLoading } = useUser();
  const [activeTab, setActiveTab] = useState("soda");
  const [selectedMonth, setSelectedMonth] = useState("2025-01");

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTransactionTypes, setSelectedTransactionTypes] = useState<
    string[]
  >([]);

  // Alert dialog state
  const [transactionToDelete, setTransactionToDelete] =
    useState<Transaction | null>(null);

  // Child dialog states
  const [kycOpen, setKycOpen] = useState(false);
  const [bankTransferOpen, setBankTransferOpen] = useState(false);
  const [ibanOpen, setIbanOpen] = useState(false);
  const [withdrawalOpen, setWithdrawalOpen] = useState(false);

  // Child dialog handlers
  const handleBankTransferClick = () => {
    if (!userData) return;

    if (userData.is_kyc_approved) {
      setBankTransferOpen(true);
    } else {
      setKycOpen(true);
    }
    if (onBankTransferClick) onBankTransferClick();
  };

  const handleWithdrawClick = () => {
    if (!userData) return;

    if (userData.is_iban_saved) {
      setWithdrawalOpen(true);
    } else {
      setIbanOpen(true);
    }
    if (onWithdrawClick) onWithdrawClick();
  };

  const handleKycSubmit = () => {
    setKycOpen(false);
    setBankTransferOpen(true);
  };

  const handleIbanSubmit = () => {
    setIbanOpen(false);
    setWithdrawalOpen(true);
  };

  // CSV Export function
  const handleCsvExport = () => {
    const transactions = getCurrentTransactions();
    if (transactions.length === 0) {
      alert("Dışa aktarılacak işlem bulunamadı.");
      return;
    }

    // Create CSV content
    const headers = [
      "İşlem Tarihi",
      "İşlem No",
      "İşlem Türü",
      "Açıklama",
      "Miktar",
      "Yeni Bakiye",
    ];
    const csvContent = [
      headers.join(","),
      ...transactions.map((transaction) =>
        [
          transaction.date,
          transaction.id,
          transaction.type,
          `"${transaction.description}"`, // Wrap in quotes to handle commas
          transaction.amount,
          transaction.newBalance,
        ].join(","),
      ),
    ].join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `${activeTab}_transactions_${selectedMonth}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle withdrawal deletion
  const handleDeleteWithdrawal = (transaction: Transaction) => {
    setTransactionToDelete(transaction);
  };

  const confirmDeleteWithdrawal = () => {
    if (transactionToDelete) {
      console.log("Deleting withdrawal request:", transactionToDelete.id);
      // TODO: Implement actual deletion logic here
      // This would typically involve calling an API to delete the withdrawal request
      setTransactionToDelete(null);
    }
  };

  // Generate months for current year
  const currentYear = new Date().getFullYear();
  const months = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1;
    const monthStr = month.toString().padStart(2, "0");
    return {
      value: `${currentYear}-${monthStr}`,
      label: new Date(currentYear, i).toLocaleDateString("tr-TR", {
        month: "long",
        year: "numeric",
      }),
    };
  });

  const getCurrentTransactions = (): Transaction[] => {
    if (!transactionData) return [];

    let transactions: Transaction[] = [];
    if (activeTab === "soda") {
      transactions = transactionData.sodaTransactions[selectedMonth] || [];
    } else {
      transactions = transactionData.capsTransactions[selectedMonth] || [];
    }

    // Apply search filter
    if (searchQuery) {
      transactions = transactions.filter(
        (transaction) =>
          transaction.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          transaction.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
          transaction.id.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    // Apply transaction type filter
    if (selectedTransactionTypes.length > 0) {
      transactions = transactions.filter((transaction) =>
        selectedTransactionTypes.includes(transaction.type),
      );
    }

    return transactions;
  };

  // Calculate balance for the selected month by finding the latest transaction's balance
  const getBalanceForMonth = (
    month: string,
    transactionData: TransactionData,
  ): number => {
    const transactions = transactionData[month] || [];
    if (transactions.length === 0) {
      // If no transactions for this month, find the latest transaction from previous months
      const allMonths = Object.keys(transactionData).sort();
      const currentMonthIndex = allMonths.indexOf(month);

      for (let i = currentMonthIndex - 1; i >= 0; i--) {
        const monthKey = allMonths[i];
        if (monthKey) {
          const prevMonthTransactions = transactionData[monthKey] || [];
          if (prevMonthTransactions.length > 0) {
            // Return the latest balance from the previous month
            const firstTransaction = prevMonthTransactions[0];
            return firstTransaction ? firstTransaction.newBalance : 0;
          }
        }
      }

      // If no previous transactions found, return 0
      return 0;
    }

    // Return the latest transaction's balance (first in array since they're sorted by date desc)
    const firstTransaction = transactions[0];
    return firstTransaction ? firstTransaction.newBalance : 0;
  };

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      historyKey={effectiveHistoryKey}
    >
      <DialogContent
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onInteractOutside={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        className="sm:max-w-[1000px] h-[75vh] max-h-[1000px] overflow-hidden w-full max-w-full flex flex-col pb-10"
      >
        <div className="w-full flex flex-col flex-1">
          <DialogHeader className="flex-row items-center justify-between gap-4">
            <DialogTitle variant="stripe" className="w-2/3">
              {"CÜZDAN"}
            </DialogTitle>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mr-10 h-15 text-2xl font-mono font-bold">
                <TabsTrigger value="soda" className="w-50 relative first:pl-4">
                  <div className="flex items-center justify-between gap-3">
                    <div className="w-14 h-14 drop-shadow-md/40">
                      <Image
                        src="/assets/soda-lg.png"
                        alt="Soda"
                        fill
                        sizes="150px"
                        className="object-contain"
                      />
                    </div>
                    <div className="text-right">
                      {balancesLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        balanceData?.sodaBalance || 0
                      )}
                    </div>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="caps" className="w-50 relative pl-4">
                  <div className="flex items-center justify-between gap-4">
                    <div className="w-12 h-12 drop-shadow-md/40">
                      <Image
                        src="/assets/cap-lg.png"
                        alt="Cap"
                        fill
                        sizes="100px"
                        className="object-contain"
                      />
                    </div>
                    <div>
                      {balancesLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        balanceData?.capsBalance || 0
                      )}
                    </div>
                  </div>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </DialogHeader>

          <div className="flex flex-col flex-1 gap-6 mx-6 mt-6">
            {/* Month selector and action buttons */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Ay seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Filter Button */}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button size="icon">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-56" align="start">
                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">İşlem Türleri</h4>
                      {[
                        "Yatırım",
                        "Çekim",
                        "Ödeme",
                        "Kazanç",
                        "Hediye",
                        "Gönderim",
                        "Talep",
                      ].map((type) => (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox
                            id={type}
                            checked={selectedTransactionTypes.includes(type)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedTransactionTypes([
                                  ...selectedTransactionTypes,
                                  type,
                                ]);
                              } else {
                                setSelectedTransactionTypes(
                                  selectedTransactionTypes.filter(
                                    (t) => t !== type,
                                  ),
                                );
                              }
                            }}
                          />
                          <label htmlFor={type} className="text-sm">
                            {type}
                          </label>
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Search Button */}
                <Search
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                  placeholder="İşlem ara..."
                  size="icon"
                  align="start"
                  className="w-72"
                />

                {/* Download Button */}
                <Button onClick={handleCsvExport} size="icon">
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex gap-3">
                {activeTab === "soda" ? (
                  <>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="small"
                          variant="default"
                          className="px-10 flex items-center gap-2"
                          disabled={userLoading}
                        >
                          {"YATIR"}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        <DropdownMenuItem onClick={handleBankTransferClick}>
                          Banka Transferi
                        </DropdownMenuItem>
                        <DropdownMenuItem disabled>
                          Kredi Kartı
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <Button
                      onClick={handleWithdrawClick}
                      size="small"
                      variant="default"
                      className="px-10"
                      disabled={userLoading}
                    >
                      {"ÇEK"}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={onEarnClick}
                      disabled
                      size="small"
                      variant="default"
                      className="px-10"
                    >
                      {"KAZAN"}
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Transactions table */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-24 min-w-[6rem] flex-shrink-0">
                    Tarih
                  </TableHead>
                  <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                    No
                  </TableHead>
                  <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                    Tür
                  </TableHead>
                  <TableHead className="flex-1 min-w-0">Açıklama</TableHead>
                  <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                    Miktar
                  </TableHead>
                  <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                    Bakiye
                  </TableHead>
                  <TableHead className="w-12 min-w-[3rem] flex-shrink-0"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactionsLoading ? (
                  // Skeleton loading rows
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-8 w-8" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : getCurrentTransactions().length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      className="text-center text-muted-foreground py-8"
                    >
                      Bu ay için işlem bulunamadı
                    </TableCell>
                  </TableRow>
                ) : (
                  getCurrentTransactions().map((transaction) => (
                    <TableRow
                      key={transaction.id}
                      className={
                        transaction.type === "Talep" ? "bg-primary/30" : ""
                      }
                    >
                      <TableCell className="w-24 min-w-[6rem] flex-shrink-0">
                        {new Date(transaction.date).toLocaleDateString("tr-TR")}
                      </TableCell>
                      <TableCell className="w-20 min-w-[5rem] flex-shrink-0 font-mono text-sm">
                        {transaction.id}
                      </TableCell>
                      <TableCell className="w-20 min-w-[5rem] flex-shrink-0">
                        {transaction.type}
                      </TableCell>
                      <TableCell className="flex-1 min-w-0 max-w-30">
                        <div
                          className="truncate"
                          title={transaction.description}
                        >
                          {transaction.description}
                        </div>
                      </TableCell>
                      <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                        <span
                          className={
                            transaction.amount >= 0
                              ? "text-success"
                              : "text-destructive"
                          }
                        >
                          {transaction.amount}
                        </span>
                      </TableCell>
                      <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right font-medium">
                        {transaction.newBalance}
                      </TableCell>
                      <TableCell className="w-12 min-w-[3rem] flex-shrink-0">
                        {transaction.type === "Talep" &&
                        (transaction as any).status === "pending" ? (
                          <button
                            className="border-none shadow-none bg-transparent p-1 hover:bg-transparent focus:outline-none text-red-600 hover:text-red-700"
                            onClick={() => handleDeleteWithdrawal(transaction)}
                          >
                            <X className="w-5 h-5" />
                          </button>
                        ) : transaction.type === "Yatırım" ||
                          transaction.type === "Çekim" ? (
                          <button className="border-none shadow-none bg-transparent p-1 hover:bg-transparent focus:outline-none text-foreground hover:text-foreground/80">
                            <ReceiptText className="w-5 h-5" />
                          </button>
                        ) : null}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>

      {/* Child Dialogs */}
      <KYCDialog
        open={kycOpen}
        onOpenChange={setKycOpen}
        onSubmit={handleKycSubmit}
      />

      <BankTransferDialog
        open={bankTransferOpen}
        onOpenChange={setBankTransferOpen}
      />

      <IbanDialog
        open={ibanOpen}
        onOpenChange={setIbanOpen}
        onSubmit={handleIbanSubmit}
      />

      <WithdrawalDialog
        open={withdrawalOpen}
        onOpenChange={setWithdrawalOpen}
        balance={0} // TODO: Get actual balance from transaction data
        min={20}
        max={10000}
        onSubmit={(data) => {
          console.log("Withdrawal submitted:", data);
          setWithdrawalOpen(false);
        }}
      />

      {/* Alert Dialog for Withdrawal Deletion */}
      <AlertDialog
        open={!!transactionToDelete}
        onOpenChange={(open) => !open && setTransactionToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Çekim Talebini İptal Et</AlertDialogTitle>
            <AlertDialogDescription>
              Bu çekim talebini iptal etmek istediğinizden emin misiniz? Bu
              işlem geri alınamaz.
              {transactionToDelete && (
                <div className="mt-2 p-2 bg-muted rounded text-sm">
                  <strong>İşlem:</strong> {transactionToDelete.description}
                  <br />
                  <strong>Miktar:</strong> {transactionToDelete.amount} TL
                  <br />
                  <strong>Tarih:</strong> {transactionToDelete.date}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Vazgeç</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteWithdrawal}
              className="bg-red-600 hover:bg-red-700"
            >
              Talebi İptal Et
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {children}
    </Dialog>
  );
}
