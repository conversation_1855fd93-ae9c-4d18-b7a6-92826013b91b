"use client";

import { <PERSON><PERSON>heckBig } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";

export interface FeedbackDialogProps {
  /** Whether the feedback dialog is open */
  open?: boolean;
  /** Callback when dialog open state changes */
  onOpenChange?: (open: boolean) => void;
  /** Title of the feedback dialog */
  title?: string;
  /** Main message displayed with the icon */
  message?: string;
  /** Additional descriptive content paragraphs */
  content?: string[];
  /** Text for the action button */
  buttonText?: string;
  /** Callback when the action button is clicked */
  onButtonClick?: () => void;
  /** Whether to show the close button (X) in the dialog */
  showCloseButton?: boolean;
  /** Custom icon component to display instead of the default success icon */
  icon?: React.ReactNode;
  /** Custom CSS class for the message text */
  messageClassName?: string;
}

export function FeedbackDialog({
  open = false,
  onOpenChange,
  title = "Başarılı!",
  message = "İşlem Tamamlandı",
  content = [],
  buttonText = "KAPAT",
  onButtonClick,
  showCloseButton = false,
  icon,
  messageClassName = "text-center text-xl text-success",
}: FeedbackDialogProps) {
  const handleButtonClick = () => {
    onButtonClick?.();
    onOpenChange?.(false);
  };

  const defaultIcon = (
    <span className="flex gap-2 items-center justify-center">
      <CircleCheckBig className="stroke-3" />
      {message}
    </span>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onInteractOutside={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        showCloseButton={showCloseButton}
        className="sm:max-w-[500px]"
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription className={messageClassName}>
            {icon || defaultIcon}
          </DialogDescription>
        </DialogHeader>

        {content.length > 0 && (
          <div className="bg-muted mx-8 text-base fake-text-stroke-muted text-muted-foreground py-6 px-8 rounded-sm space-y-4">
            {content.map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </div>
        )}

        <DialogFooter>
          <Button onClick={handleButtonClick}>{buttonText}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
